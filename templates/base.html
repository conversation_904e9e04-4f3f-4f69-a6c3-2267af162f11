<!DOCTYPE html>
<html lang="es" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' || localStorage.getItem('darkMode') === null }" 
      x-init="$watch('darkMode', val => localStorage.setItem('darkMode', val))" 
      :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestión de Tienda{% endblock %}</title>
    
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- HTMX CDN -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Configuración de TailwindCSS para modo oscuro -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Colores personalizados para las secciones
                        almacen: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        ventas: {
                            50: '#f0fdf4',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                        },
                        reportes: {
                            50: '#fff7ed',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#c2410c',
                        }
                    }
                }
            }
        }
    </script>
    
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
    <!-- Navegación superior -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo/Nombre del sistema -->
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900 dark:text-white">
                            <a href="{% url 'core:dashboard' %}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                🏪 Gestión de Tienda
                            </a>
                        </h1>
                    </div>
                </div>
                
                <!-- Controles de usuario y tema -->
                <div class="flex items-center space-x-4">
                    <!-- Toggle de tema oscuro/claro -->
                    <button @click="darkMode = !darkMode" 
                            class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                        <span x-show="!darkMode">🌙</span>
                        <span x-show="darkMode">☀️</span>
                    </button>
                    
                    <!-- Usuario actual -->
                    {% if user.is_authenticated %}
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-700 dark:text-gray-300">
                                👤 {{ user.username }}
                            </span>
                            <a href="{% url 'admin:logout' %}" 
                               class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300">
                                Salir
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <div class="flex min-h-screen">
        <!-- Sidebar de navegación -->
        <aside class="w-64 bg-white dark:bg-gray-800 shadow-lg border-r border-gray-200 dark:border-gray-700">
            <nav class="mt-8 px-4">
                <ul class="space-y-2">
                    <!-- Dashboard -->
                    <li>
                        <a href="{% url 'core:dashboard' %}" 
                           class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors {% if request.resolver_match.namespace == 'core' and request.resolver_match.url_name == 'dashboard' %}bg-gray-100 dark:bg-gray-700{% endif %}">
                            <span class="mr-3">📊</span>
                            Dashboard
                        </a>
                    </li>
                    
                    <!-- Almacén -->
                    <li>
                        <a href="{% url 'almacen:productos' %}" 
                           class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-almacen-50 dark:hover:bg-almacen-700 hover:text-almacen-700 dark:hover:text-almacen-300 transition-colors {% if request.resolver_match.namespace == 'almacen' %}bg-almacen-50 dark:bg-almacen-700 text-almacen-700 dark:text-almacen-300{% endif %}">
                            <span class="mr-3">📦</span>
                            Almacén
                        </a>
                    </li>
                    
                    <!-- Ventas -->
                    <li>
                        <a href="{% url 'ventas:turnos' %}" 
                           class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-ventas-50 dark:hover:bg-ventas-700 hover:text-ventas-700 dark:hover:text-ventas-300 transition-colors {% if request.resolver_match.namespace == 'ventas' %}bg-ventas-50 dark:bg-ventas-700 text-ventas-700 dark:text-ventas-300{% endif %}">
                            <span class="mr-3">💰</span>
                            Ventas
                        </a>
                    </li>
                    
                    <!-- Reportes -->
                    <li>
                        <a href="{% url 'reportes:dashboard' %}" 
                           class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-reportes-50 dark:hover:bg-reportes-700 hover:text-reportes-700 dark:hover:text-reportes-300 transition-colors {% if request.resolver_match.namespace == 'reportes' %}bg-reportes-50 dark:bg-reportes-700 text-reportes-700 dark:text-reportes-300{% endif %}">
                            <span class="mr-3">📈</span>
                            Reportes
                        </a>
                    </li>
                    
                    <!-- Configuración -->
                    <li>
                        <a href="{% url 'core:configuracion' %}" 
                           class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors {% if request.resolver_match.namespace == 'core' and request.resolver_match.url_name == 'configuracion' %}bg-gray-100 dark:bg-gray-700{% endif %}">
                            <span class="mr-3">⚙️</span>
                            Configuración
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Contenido principal -->
        <main class="flex-1 p-8">
            <!-- Breadcrumb -->
            {% block breadcrumb %}
            <nav class="flex mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{% url 'core:dashboard' %}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                            🏠 Inicio
                        </a>
                    </li>
                    {% if breadcrumb_items %}
                        {% for item in breadcrumb_items %}
                        <li>
                            <div class="flex items-center">
                                <span class="mx-2 text-gray-400">/</span>
                                {% if item.url %}
                                    <a href="{{ item.url }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                                        {{ item.name }}
                                    </a>
                                {% else %}
                                    <span class="text-gray-500 dark:text-gray-400">{{ item.name }}</span>
                                {% endif %}
                            </div>
                        </li>
                        {% endfor %}
                    {% endif %}
                </ol>
            </nav>
            {% endblock %}

            <!-- Mensajes del sistema -->
            {% if messages %}
                <div class="mb-6">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} p-4 mb-4 rounded-lg border {% if message.tags == 'error' %}bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200{% elif message.tags == 'warning' %}bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200{% elif message.tags == 'success' %}bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200{% else %}bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200{% endif %}" 
                             x-data="{ show: true }" 
                             x-show="show" 
                             x-transition>
                            <div class="flex justify-between items-center">
                                <span>{{ message }}</span>
                                <button @click="show = false" class="ml-4 text-lg font-bold">&times;</button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Contenido de la página -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Scripts adicionales -->
    {% block extra_scripts %}{% endblock %}
</body>
</html>
