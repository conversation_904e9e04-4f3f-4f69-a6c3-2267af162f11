{% extends 'base.html' %}

{% block title %}Configuración - Gestión de Tienda{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- T<PERSON><PERSON>lo principal -->
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            ⚙️ Configuración del Sistema
        </h1>
    </div>

    <!-- Secciones de configuración -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- Gestión de usuarios -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">👥</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Gestión de Usuarios
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Administra usuarios, permisos y roles del sistema
                </p>
                <a href="{% url 'admin:auth_user_changelist' %}" class="btn-primary">
                    Gestionar Usuarios
                </a>
            </div>
        </div>

        <!-- Categorías de productos -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">🏷️</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Categorías
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Administra las categorías de productos
                </p>
                <a href="{% url 'almacen:categorias' %}" class="btn-secondary">
                    Gestionar Categorías
                </a>
            </div>
        </div>

        <!-- Configuración del sistema -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">🔧</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Panel de Administración
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Acceso completo al panel de administración de Django
                </p>
                <a href="{% url 'admin:index' %}" class="btn-warning">
                    Ir al Admin
                </a>
            </div>
        </div>

        <!-- Respaldo de datos -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">💾</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Respaldo de Datos
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Exporta e importa datos del sistema
                </p>
                <button class="btn-secondary" onclick="alert('Funcionalidad en desarrollo')">
                    Gestionar Respaldos
                </button>
            </div>
        </div>

        <!-- Configuración de turnos -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">🕐</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Configuración de Turnos
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Configura horarios y tipos de turnos
                </p>
                <button class="btn-secondary" onclick="alert('Funcionalidad en desarrollo')">
                    Configurar Turnos
                </button>
            </div>
        </div>

        <!-- Reportes automáticos -->
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">📊</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Reportes Automáticos
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Configura reportes automáticos por email
                </p>
                <button class="btn-secondary" onclick="alert('Funcionalidad en desarrollo')">
                    Configurar Reportes
                </button>
            </div>
        </div>
    </div>

    <!-- Información del sistema -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                ℹ️ Información del Sistema
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Detalles del Sistema</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Versión:</dt>
                            <dd class="text-gray-900 dark:text-white">1.0.0</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Framework:</dt>
                            <dd class="text-gray-900 dark:text-white">Django 5.2.1</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Base de datos:</dt>
                            <dd class="text-gray-900 dark:text-white">SQLite</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Última actualización:</dt>
                            <dd class="text-gray-900 dark:text-white">{{ "now"|date:"d/m/Y" }}</dd>
                        </div>
                    </dl>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Estadísticas de Uso</h4>
                    <dl class="space-y-2">
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Usuario actual:</dt>
                            <dd class="text-gray-900 dark:text-white">{{ user.username }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Último acceso:</dt>
                            <dd class="text-gray-900 dark:text-white">{{ user.last_login|date:"d/m/Y H:i" }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-gray-500 dark:text-gray-400">Tema actual:</dt>
                            <dd class="text-gray-900 dark:text-white">
                                <span x-text="darkMode ? 'Oscuro' : 'Claro'"></span>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Acciones rápidas -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                ⚡ Acciones Rápidas
            </h3>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="btn-primary" onclick="window.location.reload()">
                    🔄 Recargar Página
                </button>
                <button class="btn-secondary" onclick="localStorage.clear(); alert('Cache limpiado')">
                    🗑️ Limpiar Cache
                </button>
                <button class="btn-warning" onclick="window.print()">
                    🖨️ Imprimir Página
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
