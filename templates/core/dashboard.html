{% extends 'base.html' %}

{% block title %}Dashboard - Gestión de Tienda{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- <PERSON><PERSON><PERSON><PERSON> principal -->
    <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            📊 Dashboard Principal
        </h1>
        <div class="text-sm text-gray-500 dark:text-gray-400">
            Última actualización: {{ "now"|date:"d/m/Y H:i" }}
        </div>
    </div>

    <!-- Tarjetas de métricas principales -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total de productos -->
        <div class="metric-card bg-almacen">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <span class="text-white text-lg">📦</span>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Total Productos
                        </dt>
                        <dd class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ total_productos }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Productos bajo stock -->
        <div class="metric-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <span class="text-white text-lg">⚠️</span>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Bajo Stock
                        </dt>
                        <dd class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ productos_bajo_stock }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Ventas del día -->
        <div class="metric-card bg-ventas">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <span class="text-white text-lg">💰</span>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Ventas Hoy
                        </dt>
                        <dd class="text-lg font-medium text-gray-900 dark:text-white">
                            ${{ total_ventas_hoy|floatformat:2 }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Valor del inventario -->
        <div class="metric-card bg-reportes">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <span class="text-white text-lg">💎</span>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            Valor Inventario
                        </dt>
                        <dd class="text-lg font-medium text-gray-900 dark:text-white">
                            ${{ valor_inventario|floatformat:2 }}
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <!-- Sección de turnos del día -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Turnos de hoy -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    🕐 Turnos de Hoy
                </h3>
            </div>
            <div class="card-body">
                {% if turnos_hoy %}
                    <div class="space-y-3">
                        {% for turno in turnos_hoy %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">
                                    {{ turno.get_tipo_display }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ turno.vendedor.username }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-gray-900 dark:text-white">
                                    ${{ turno.total_ventas|floatformat:2 }}
                                </div>
                                <span class="badge {% if turno.estado == 'abierto' %}badge-success{% else %}badge-secondary{% endif %}">
                                    {{ turno.get_estado_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <span class="text-4xl">📅</span>
                        <p class="mt-2">No hay turnos registrados para hoy</p>
                        <a href="{% url 'ventas:turno_nuevo' %}" class="btn-primary mt-4 inline-block">
                            Crear Turno
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Productos que necesitan restock -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    ⚠️ Productos Bajo Stock
                </h3>
            </div>
            <div class="card-body">
                {% if productos_restock %}
                    <div class="space-y-3">
                        {% for producto in productos_restock %}
                        <div class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900 rounded-lg">
                            <div>
                                <div class="font-medium text-gray-900 dark:text-white">
                                    {{ producto.nombre }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    {{ producto.codigo }}
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-red-600 dark:text-red-400">
                                    {{ producto.stock_actual }} {{ producto.unidad_medida }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                    Mín: {{ producto.stock_minimo }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="mt-4 text-center">
                        <a href="{% url 'almacen:productos' %}" class="btn-primary">
                            Ver Todos los Productos
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                        <span class="text-4xl">✅</span>
                        <p class="mt-2">Todos los productos tienen stock suficiente</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Movimientos recientes del almacén -->
    <div class="card">
        <div class="card-header">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                📋 Movimientos Recientes del Almacén
            </h3>
        </div>
        <div class="card-body">
            {% if movimientos_recientes %}
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Producto</th>
                                <th>Tipo</th>
                                <th>Cantidad</th>
                                <th>Usuario</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movimiento in movimientos_recientes %}
                            <tr>
                                <td>{{ movimiento.fecha|date:"d/m/Y H:i" }}</td>
                                <td>{{ movimiento.producto.nombre }}</td>
                                <td>
                                    <span class="badge {% if movimiento.tipo == 'entrada' %}badge-success{% else %}badge-warning{% endif %}">
                                        {{ movimiento.get_tipo_display }}
                                    </span>
                                </td>
                                <td>{{ movimiento.cantidad }} {{ movimiento.producto.unidad_medida }}</td>
                                <td>{{ movimiento.usuario.username }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-center">
                    <a href="{% url 'almacen:movimientos' %}" class="btn-secondary">
                        Ver Todos los Movimientos
                    </a>
                </div>
            {% else %}
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <span class="text-4xl">📦</span>
                    <p class="mt-2">No hay movimientos registrados</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Accesos rápidos -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">📦</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Gestión de Almacén
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Administra productos, stock y movimientos
                </p>
                <a href="{% url 'almacen:productos' %}" class="btn-primary">
                    Ir a Almacén
                </a>
            </div>
        </div>

        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">💰</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Gestión de Ventas
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Administra turnos, ventas y entregas
                </p>
                <a href="{% url 'ventas:turnos' %}" class="btn-success">
                    Ir a Ventas
                </a>
            </div>
        </div>

        <div class="card hover:shadow-lg transition-shadow">
            <div class="card-body text-center">
                <div class="text-4xl mb-4">📈</div>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Reportes y Estadísticas
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Analiza ventas, ganancias y tendencias
                </p>
                <a href="{% url 'reportes:dashboard' %}" class="btn-warning">
                    Ver Reportes
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
