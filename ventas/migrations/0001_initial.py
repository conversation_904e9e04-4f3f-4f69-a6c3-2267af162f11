# Generated by Django 5.2.1 on 2025-05-27 18:30

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('almacen', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Turno',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fecha', models.DateField(verbose_name='Fecha')),
                ('tipo', models.CharField(choices=[('manana', 'Mañana'), ('noche', 'Noche')], max_length=10, verbose_name='Tipo de turno')),
                ('estado', models.CharField(choices=[('abierto', 'Abierto'), ('cerrado', 'Cerrado')], default='abierto', max_length=10, verbose_name='Estado')),
                ('hora_inicio', models.DateTimeField(auto_now_add=True, verbose_name='Hora de inicio')),
                ('hora_cierre', models.DateTimeField(blank=True, null=True, verbose_name='Hora de cierre')),
                ('observaciones', models.TextField(blank=True, verbose_name='Observaciones')),
                ('vendedor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Vendedor')),
            ],
            options={
                'verbose_name': 'Turno',
                'verbose_name_plural': 'Turnos',
                'ordering': ['-fecha', '-tipo'],
                'unique_together': {('fecha', 'tipo')},
            },
        ),
        migrations.CreateModel(
            name='Venta',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cantidad', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Cantidad')),
                ('precio_unitario', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Precio unitario')),
                ('descuento', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Descuento (%)')),
                ('fecha_hora', models.DateTimeField(auto_now_add=True, verbose_name='Fecha y hora')),
                ('observaciones', models.TextField(blank=True, verbose_name='Observaciones')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='almacen.producto', verbose_name='Producto')),
                ('turno', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ventas', to='ventas.turno', verbose_name='Turno')),
            ],
            options={
                'verbose_name': 'Venta',
                'verbose_name_plural': 'Ventas',
                'ordering': ['-fecha_hora'],
            },
        ),
        migrations.CreateModel(
            name='ProductoTurno',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_inicial', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Stock inicial')),
                ('entrada_almacen', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Entrada desde almacén')),
                ('precio_venta', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Precio de venta')),
                ('stock_final', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Stock final')),
                ('descarte', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Descarte')),
                ('devolucion_almacen', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Devolución a almacén')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='almacen.producto', verbose_name='Producto')),
                ('turno', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='productos_turno', to='ventas.turno', verbose_name='Turno')),
            ],
            options={
                'verbose_name': 'Producto en turno',
                'verbose_name_plural': 'Productos en turno',
                'unique_together': {('turno', 'producto')},
            },
        ),
    ]
