from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from .models import Turno, <PERSON>enta, ProductoTurno


@login_required
def turnos(request):
    """Vista para listar turnos"""
    turnos_list = Turno.objects.select_related('vendedor').order_by('-fecha', '-tipo')

    context = {
        'turnos': turnos_list,
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': None}
        ]
    }

    return render(request, 'ventas/turnos.html', context)


@login_required
def turno_nuevo(request):
    """Vista para crear un nuevo turno"""
    if request.method == 'POST':
        # Lógica para crear turno
        fecha = request.POST.get('fecha')
        tipo = request.POST.get('tipo')

        # Verificar que no exista un turno para esa fecha y tipo
        if Turno.objects.filter(fecha=fecha, tipo=tipo).exists():
            messages.error(request, 'Ya existe un turno para esa fecha y tipo.')
        else:
            turno = Turno.objects.create(
                fecha=fecha,
                tipo=tipo,
                vendedor=request.user
            )
            messages.success(request, f'Turno {turno.get_tipo_display()} creado exitosamente.')
            return redirect('ventas:turno_detalle', pk=turno.pk)

    context = {
        'titulo': 'Nuevo Turno',
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': 'ventas:turnos'},
            {'name': 'Nuevo Turno', 'url': None}
        ]
    }

    return render(request, 'ventas/turno_form.html', context)


@login_required
def turno_detalle(request, pk):
    """Vista para ver detalles de un turno"""
    turno = get_object_or_404(Turno, pk=pk)
    productos_turno = ProductoTurno.objects.filter(turno=turno).select_related('producto')
    ventas = Venta.objects.filter(turno=turno).select_related('producto')

    context = {
        'turno': turno,
        'productos_turno': productos_turno,
        'ventas': ventas,
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': 'ventas:turnos'},
            {'name': f'Turno {turno.get_tipo_display()}', 'url': None}
        ]
    }

    return render(request, 'ventas/turno_detalle.html', context)


@login_required
def turno_cerrar(request, pk):
    """Vista para cerrar un turno"""
    turno = get_object_or_404(Turno, pk=pk)

    if turno.estado == 'cerrado':
        messages.warning(request, 'Este turno ya está cerrado.')
        return redirect('ventas:turno_detalle', pk=pk)

    if request.method == 'POST':
        turno.estado = 'cerrado'
        turno.hora_cierre = timezone.now()
        turno.observaciones = request.POST.get('observaciones', '')
        turno.save()

        messages.success(request, 'Turno cerrado exitosamente.')
        return redirect('ventas:turno_detalle', pk=pk)

    context = {
        'turno': turno,
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': 'ventas:turnos'},
            {'name': f'Turno {turno.get_tipo_display()}', 'url': 'ventas:turno_detalle'},
            {'name': 'Cerrar', 'url': None}
        ]
    }

    return render(request, 'ventas/turno_cerrar.html', context)


@login_required
def ventas(request):
    """Vista para listar ventas"""
    ventas_list = Venta.objects.select_related('turno', 'producto').order_by('-fecha_hora')

    context = {
        'ventas': ventas_list,
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': 'ventas:turnos'},
            {'name': 'Registro de Ventas', 'url': None}
        ]
    }

    return render(request, 'ventas/ventas.html', context)


@login_required
def venta_nueva(request):
    """Vista para registrar una nueva venta"""
    if request.method == 'POST':
        # Lógica para crear venta
        messages.success(request, 'Venta registrada exitosamente.')
        return redirect('ventas:ventas')

    context = {
        'titulo': 'Nueva Venta',
        'breadcrumb_items': [
            {'name': 'Ventas', 'url': 'ventas:turnos'},
            {'name': 'Registro de Ventas', 'url': 'ventas:ventas'},
            {'name': 'Nueva Venta', 'url': None}
        ]
    }

    return render(request, 'ventas/venta_form.html', context)
