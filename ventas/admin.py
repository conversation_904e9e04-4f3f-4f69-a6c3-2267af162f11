from django.contrib import admin
from .models import Turno, ProductoTurno, Venta


@admin.register(Turno)
class TurnoAdmin(admin.ModelAdmin):
    """Administración de turnos"""
    list_display = ['fecha', 'tipo', 'vendedor', 'estado', 'hora_inicio', 'hora_cierre']
    list_filter = ['tipo', 'estado', 'fecha']
    search_fields = ['vendedor__username']
    ordering = ['-fecha', '-tipo']
    readonly_fields = ['hora_inicio']


@admin.register(ProductoTurno)
class ProductoTurnoAdmin(admin.ModelAdmin):
    """Administración de productos en turnos"""
    list_display = ['turno', 'producto', 'stock_inicial', 'entrada_almacen', 'precio_venta', 'stock_final']
    list_filter = ['turno__fecha', 'turno__tipo']
    search_fields = ['producto__nombre', 'turno__vendedor__username']
    ordering = ['-turno__fecha']


@admin.register(Venta)
class VentaAdmin(admin.ModelAdmin):
    """Administración de ventas"""
    list_display = ['fecha_hora', 'turno', 'producto', 'cantidad', 'precio_unitario', 'total']
    list_filter = ['fecha_hora', 'turno__tipo']
    search_fields = ['producto__nombre', 'turno__vendedor__username']
    ordering = ['-fecha_hora']
    readonly_fields = ['fecha_hora', 'subtotal', 'monto_descuento', 'total']
