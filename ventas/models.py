from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from almacen.models import Producto


class Turno(models.Model):
    """Modelo para turnos de venta"""
    TIPOS_TURNO = [
        ('manana', 'Ma<PERSON><PERSON>'),
        ('noche', 'Noche'),
    ]

    ESTADOS = [
        ('abierto', 'Abierto'),
        ('cerrado', 'Cerrado'),
    ]

    fecha = models.DateField(verbose_name="Fecha")
    tipo = models.CharField(max_length=10, choices=TIPOS_TURNO, verbose_name="Tipo de turno")
    vendedor = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="Vendedor")
    estado = models.CharField(max_length=10, choices=ESTADOS, default='abierto', verbose_name="Estado")
    hora_inicio = models.DateTimeField(auto_now_add=True, verbose_name="Hora de inicio")
    hora_cierre = models.DateTimeField(null=True, blank=True, verbose_name="Hora de cierre")
    observaciones = models.TextField(blank=True, verbose_name="Observaciones")

    class Meta:
        verbose_name = "Turno"
        verbose_name_plural = "Turnos"
        ordering = ['-fecha', '-tipo']
        unique_together = ['fecha', 'tipo']

    def __str__(self):
        return f"{self.fecha} - {self.get_tipo_display()} - {self.vendedor.username}"

    @property
    def total_ventas(self):
        """Calcula el total de ventas del turno"""
        return sum(venta.total for venta in self.ventas.all())

    @property
    def total_productos_vendidos(self):
        """Calcula el total de productos vendidos en el turno"""
        return sum(venta.cantidad for venta in self.ventas.all())


class ProductoTurno(models.Model):
    """Modelo para productos disponibles en un turno específico"""
    turno = models.ForeignKey(Turno, on_delete=models.CASCADE, related_name='productos_turno', verbose_name="Turno")
    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, verbose_name="Producto")
    stock_inicial = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Stock inicial")
    entrada_almacen = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Entrada desde almacén")
    precio_venta = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Precio de venta")
    stock_final = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Stock final")
    descarte = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Descarte")
    devolucion_almacen = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Devolución a almacén")

    class Meta:
        verbose_name = "Producto en turno"
        verbose_name_plural = "Productos en turno"
        unique_together = ['turno', 'producto']

    def __str__(self):
        return f"{self.turno} - {self.producto.nombre}"

    @property
    def stock_disponible(self):
        """Calcula el stock disponible para venta"""
        return self.stock_inicial + self.entrada_almacen

    @property
    def cantidad_vendida(self):
        """Calcula la cantidad vendida"""
        return self.stock_disponible - self.stock_final - self.descarte - self.devolucion_almacen

    @property
    def total_ventas(self):
        """Calcula el total de ventas de este producto en el turno"""
        return self.cantidad_vendida * self.precio_venta


class Venta(models.Model):
    """Modelo para registrar ventas individuales"""
    turno = models.ForeignKey(Turno, on_delete=models.CASCADE, related_name='ventas', verbose_name="Turno")
    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, verbose_name="Producto")
    cantidad = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Cantidad")
    precio_unitario = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Precio unitario")
    descuento = models.DecimalField(max_digits=5, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Descuento (%)")
    fecha_hora = models.DateTimeField(auto_now_add=True, verbose_name="Fecha y hora")
    observaciones = models.TextField(blank=True, verbose_name="Observaciones")

    class Meta:
        verbose_name = "Venta"
        verbose_name_plural = "Ventas"
        ordering = ['-fecha_hora']

    def __str__(self):
        return f"Venta {self.id} - {self.producto.nombre} - {self.cantidad}"

    @property
    def subtotal(self):
        """Calcula el subtotal sin descuento"""
        return self.cantidad * self.precio_unitario

    @property
    def monto_descuento(self):
        """Calcula el monto del descuento"""
        return (self.subtotal * self.descuento) / 100

    @property
    def total(self):
        """Calcula el total con descuento aplicado"""
        return self.subtotal - self.monto_descuento
