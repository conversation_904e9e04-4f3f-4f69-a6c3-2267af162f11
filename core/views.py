from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from almacen.models import Producto, MovimientoAlmacen
from ventas.models import Turno, Venta


@login_required
def dashboard(request):
    """Vista principal del dashboard con indicadores generales"""

    # Obtener fecha actual
    hoy = timezone.now().date()

    # Estadísticas del almacén
    total_productos = Producto.objects.filter(activo=True).count()
    productos_bajo_stock = Producto.objects.filter(
        activo=True,
        stock_actual__lte=F('stock_minimo')
    ).count()

    # Calcular valor total del inventario
    valor_inventario = Producto.objects.filter(activo=True).aggregate(
        total=Sum(F('stock_actual') * F('precio_compra'))
    )['total'] or 0

    # Estadísticas de ventas del día
    turnos_hoy = Turno.objects.filter(fecha=hoy)
    ventas_hoy = Venta.objects.filter(turno__fecha=hoy)

    total_ventas_hoy = ventas_hoy.aggregate(
        total=Sum(F('cantidad') * F('precio_unitario'))
    )['total'] or 0

    productos_vendidos_hoy = ventas_hoy.aggregate(
        total=Sum('cantidad')
    )['total'] or 0

    # Movimientos recientes del almacén
    movimientos_recientes = MovimientoAlmacen.objects.select_related(
        'producto', 'usuario'
    ).order_by('-fecha')[:10]

    # Productos que necesitan restock
    productos_restock = Producto.objects.filter(
        activo=True,
        stock_actual__lte=F('stock_minimo')
    ).order_by('stock_actual')[:10]

    # Ventas de la semana
    inicio_semana = hoy - timedelta(days=7)
    ventas_semana = []
    for i in range(7):
        fecha = inicio_semana + timedelta(days=i)
        ventas_dia = Venta.objects.filter(turno__fecha=fecha).aggregate(
            total=Sum(F('cantidad') * F('precio_unitario'))
        )['total'] or 0
        ventas_semana.append({
            'fecha': fecha,
            'total': ventas_dia
        })

    context = {
        'total_productos': total_productos,
        'productos_bajo_stock': productos_bajo_stock,
        'valor_inventario': valor_inventario,
        'total_ventas_hoy': total_ventas_hoy,
        'productos_vendidos_hoy': productos_vendidos_hoy,
        'turnos_hoy': turnos_hoy,
        'movimientos_recientes': movimientos_recientes,
        'productos_restock': productos_restock,
        'ventas_semana': ventas_semana,
        'breadcrumb_items': [
            {'name': 'Dashboard', 'url': None}
        ]
    }

    return render(request, 'core/dashboard.html', context)


@login_required
def configuracion(request):
    """Vista de configuración del sistema"""

    context = {
        'breadcrumb_items': [
            {'name': 'Configuración', 'url': None}
        ]
    }

    return render(request, 'core/configuracion.html', context)
