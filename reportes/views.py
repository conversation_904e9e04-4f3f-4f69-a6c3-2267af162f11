from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Su<PERSON>, Count, F
from django.utils import timezone
from datetime import datetime, timedelta
from almacen.models import Producto, MovimientoAlmacen
from ventas.models import Turno, <PERSON>enta


@login_required
def dashboard(request):
    """Vista principal de reportes"""

    context = {
        'breadcrumb_items': [
            {'name': 'Reportes', 'url': None}
        ]
    }

    return render(request, 'reportes/dashboard.html', context)


@login_required
def reporte_ventas(request):
    """Vista para reporte de ventas"""

    context = {
        'breadcrumb_items': [
            {'name': 'Reportes', 'url': 'reportes:dashboard'},
            {'name': 'Ventas', 'url': None}
        ]
    }

    return render(request, 'reportes/ventas.html', context)


@login_required
def reporte_inventario(request):
    """Vista para reporte de inventario"""

    context = {
        'breadcrumb_items': [
            {'name': 'Reportes', 'url': 'reportes:dashboard'},
            {'name': 'Inventario', 'url': None}
        ]
    }

    return render(request, 'reportes/inventario.html', context)


@login_required
def reporte_ganancias(request):
    """Vista para reporte de ganancias"""

    context = {
        'breadcrumb_items': [
            {'name': 'Reportes', 'url': 'reportes:dashboard'},
            {'name': 'Ganancias', 'url': None}
        ]
    }

    return render(request, 'reportes/ganancias.html', context)
