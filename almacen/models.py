from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal


class Categoria(models.Model):
    """Modelo para categorías de productos"""
    nombre = models.CharField(max_length=100, unique=True, verbose_name="Nombre")
    descripcion = models.TextField(blank=True, verbose_name="Descripción")
    activo = models.BooleanField(default=True, verbose_name="Activo")
    fecha_creacion = models.DateTimeField(auto_now_add=True, verbose_name="Fecha de creación")

    class Meta:
        verbose_name = "Categoría"
        verbose_name_plural = "Categorías"
        ordering = ['nombre']

    def __str__(self):
        return self.nombre


class Producto(models.Model):
    """Modelo para productos del almacén"""
    UNIDADES_MEDIDA = [
        ('unidad', 'Unidad'),
        ('kg', 'Kilogramo'),
        ('litro', '<PERSON>tro'),
        ('caja', 'Caja'),
        ('paquete', 'Paquete'),
    ]

    nombre = models.CharField(max_length=200, verbose_name="Nombre del producto")
    codigo = models.CharField(max_length=50, unique=True, blank=True, verbose_name="Código")
    categoria = models.ForeignKey(Categoria, on_delete=models.CASCADE, verbose_name="Categoría")
    unidad_medida = models.CharField(max_length=20, choices=UNIDADES_MEDIDA, default='unidad', verbose_name="Unidad de medida")
    precio_compra = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Precio de compra")
    precio_venta = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Precio de venta")
    stock_actual = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Stock actual")
    stock_minimo = models.DecimalField(max_digits=10, decimal_places=2, default=0, validators=[MinValueValidator(Decimal('0'))], verbose_name="Stock mínimo")
    activo = models.BooleanField(default=True, verbose_name="Activo")
    fecha_creacion = models.DateTimeField(auto_now_add=True, verbose_name="Fecha de creación")
    fecha_modificacion = models.DateTimeField(auto_now=True, verbose_name="Fecha de modificación")

    class Meta:
        verbose_name = "Producto"
        verbose_name_plural = "Productos"
        ordering = ['nombre']

    def __str__(self):
        return f"{self.nombre} ({self.codigo})"

    def save(self, *args, **kwargs):
        # Generar código automáticamente si no se proporciona
        if not self.codigo:
            # Obtener las primeras 3 letras del nombre y agregar un número secuencial
            base_code = self.nombre[:3].upper()
            count = Producto.objects.filter(codigo__startswith=base_code).count()
            self.codigo = f"{base_code}{count + 1:03d}"
        super().save(*args, **kwargs)

    @property
    def necesita_restock(self):
        """Verifica si el producto necesita restock"""
        return self.stock_actual <= self.stock_minimo

    @property
    def margen_ganancia(self):
        """Calcula el margen de ganancia"""
        if self.precio_compra > 0:
            return ((self.precio_venta - self.precio_compra) / self.precio_compra) * 100
        return 0


class MovimientoAlmacen(models.Model):
    """Modelo para registrar movimientos de entrada y salida del almacén"""
    TIPOS_MOVIMIENTO = [
        ('entrada', 'Entrada'),
        ('salida', 'Salida'),
    ]

    MOTIVOS = [
        ('compra', 'Compra'),
        ('devolucion', 'Devolución'),
        ('ajuste_inventario', 'Ajuste de inventario'),
        ('venta', 'Venta'),
        ('descarte', 'Descarte'),
        ('traslado', 'Traslado'),
        ('otro', 'Otro'),
    ]

    producto = models.ForeignKey(Producto, on_delete=models.CASCADE, verbose_name="Producto")
    tipo = models.CharField(max_length=10, choices=TIPOS_MOVIMIENTO, verbose_name="Tipo de movimiento")
    motivo = models.CharField(max_length=20, choices=MOTIVOS, verbose_name="Motivo")
    cantidad = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))], verbose_name="Cantidad")
    precio_unitario = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="Precio unitario")
    observaciones = models.TextField(blank=True, verbose_name="Observaciones")
    usuario = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="Usuario")
    fecha = models.DateTimeField(auto_now_add=True, verbose_name="Fecha")

    class Meta:
        verbose_name = "Movimiento de almacén"
        verbose_name_plural = "Movimientos de almacén"
        ordering = ['-fecha']

    def __str__(self):
        return f"{self.tipo.title()} - {self.producto.nombre} - {self.cantidad} {self.producto.unidad_medida}"

    def save(self, *args, **kwargs):
        # Actualizar stock del producto
        if self.pk is None:  # Solo para nuevos movimientos
            if self.tipo == 'entrada':
                self.producto.stock_actual += self.cantidad
            else:  # salida
                self.producto.stock_actual -= self.cantidad
            self.producto.save()
        super().save(*args, **kwargs)
