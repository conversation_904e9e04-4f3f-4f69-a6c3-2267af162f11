from django import forms
from .models import Producto, Categoria, MovimientoAlmacen


class ProductoForm(forms.ModelForm):
    """Formulario para crear y editar productos"""
    
    class Meta:
        model = Producto
        fields = [
            'nombre', 'categoria', 'unidad_medida', 'precio_compra', 
            'precio_venta', 'stock_actual', 'stock_minimo'
        ]
        widgets = {
            'nombre': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Nombre del producto'
            }),
            'categoria': forms.Select(attrs={
                'class': 'form-select'
            }),
            'unidad_medida': forms.Select(attrs={
                'class': 'form-select'
            }),
            'precio_compra': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0'
            }),
            'precio_venta': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0'
            }),
            'stock_actual': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0'
            }),
            'stock_minimo': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0'
            }),
        }
        labels = {
            'nombre': 'Nombre del producto',
            'categoria': 'Categoría',
            'unidad_medida': 'Unidad de medida',
            'precio_compra': 'Precio de compra',
            'precio_venta': 'Precio de venta',
            'stock_actual': 'Stock actual',
            'stock_minimo': 'Stock mínimo',
        }


class CategoriaForm(forms.ModelForm):
    """Formulario para crear y editar categorías"""
    
    class Meta:
        model = Categoria
        fields = ['nombre', 'descripcion']
        widgets = {
            'nombre': forms.TextInput(attrs={
                'class': 'form-input',
                'placeholder': 'Nombre de la categoría'
            }),
            'descripcion': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Descripción de la categoría (opcional)'
            }),
        }
        labels = {
            'nombre': 'Nombre de la categoría',
            'descripcion': 'Descripción',
        }


class MovimientoAlmacenForm(forms.ModelForm):
    """Formulario para registrar movimientos de almacén"""
    
    class Meta:
        model = MovimientoAlmacen
        fields = ['producto', 'tipo', 'motivo', 'cantidad', 'precio_unitario', 'observaciones']
        widgets = {
            'producto': forms.Select(attrs={
                'class': 'form-select'
            }),
            'tipo': forms.Select(attrs={
                'class': 'form-select'
            }),
            'motivo': forms.Select(attrs={
                'class': 'form-select'
            }),
            'cantidad': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0.01'
            }),
            'precio_unitario': forms.NumberInput(attrs={
                'class': 'form-input',
                'step': '0.01',
                'min': '0'
            }),
            'observaciones': forms.Textarea(attrs={
                'class': 'form-textarea',
                'rows': 3,
                'placeholder': 'Observaciones adicionales (opcional)'
            }),
        }
        labels = {
            'producto': 'Producto',
            'tipo': 'Tipo de movimiento',
            'motivo': 'Motivo',
            'cantidad': 'Cantidad',
            'precio_unitario': 'Precio unitario',
            'observaciones': 'Observaciones',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filtrar solo productos activos
        self.fields['producto'].queryset = Producto.objects.filter(activo=True)
        # Hacer precio_unitario opcional
        self.fields['precio_unitario'].required = False
