from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from .models import Producto, Categoria, MovimientoAlmacen
from .forms import ProductoForm, CategoriaForm, MovimientoAlmacenForm


@login_required
def productos(request):
    """Vista para listar productos del almacén"""
    query = request.GET.get('q', '')
    categoria_id = request.GET.get('categoria', '')

    productos_list = Producto.objects.filter(activo=True).select_related('categoria')

    if query:
        productos_list = productos_list.filter(
            Q(nombre__icontains=query) |
            Q(codigo__icontains=query)
        )

    if categoria_id:
        productos_list = productos_list.filter(categoria_id=categoria_id)

    categorias = Categoria.objects.filter(activo=True)

    context = {
        'productos': productos_list,
        'categorias': categorias,
        'query': query,
        'categoria_seleccionada': categoria_id,
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': None}
        ]
    }

    return render(request, 'almacen/productos.html', context)


@login_required
def producto_nuevo(request):
    """Vista para crear un nuevo producto"""
    if request.method == 'POST':
        form = ProductoForm(request.POST)
        if form.is_valid():
            producto = form.save()
            messages.success(request, f'Producto "{producto.nombre}" creado exitosamente.')
            return redirect('almacen:productos')
    else:
        form = ProductoForm()

    context = {
        'form': form,
        'titulo': 'Nuevo Producto',
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': 'Nuevo Producto', 'url': None}
        ]
    }

    return render(request, 'almacen/producto_form.html', context)


@login_required
def producto_detalle(request, pk):
    """Vista para ver detalles de un producto"""
    producto = get_object_or_404(Producto, pk=pk)
    movimientos = MovimientoAlmacen.objects.filter(producto=producto).order_by('-fecha')[:10]

    context = {
        'producto': producto,
        'movimientos': movimientos,
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': producto.nombre, 'url': None}
        ]
    }

    return render(request, 'almacen/producto_detalle.html', context)


@login_required
def producto_editar(request, pk):
    """Vista para editar un producto"""
    producto = get_object_or_404(Producto, pk=pk)

    if request.method == 'POST':
        form = ProductoForm(request.POST, instance=producto)
        if form.is_valid():
            producto = form.save()
            messages.success(request, f'Producto "{producto.nombre}" actualizado exitosamente.')
            return redirect('almacen:producto_detalle', pk=producto.pk)
    else:
        form = ProductoForm(instance=producto)

    context = {
        'form': form,
        'producto': producto,
        'titulo': f'Editar {producto.nombre}',
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': producto.nombre, 'url': 'almacen:producto_detalle'},
            {'name': 'Editar', 'url': None}
        ]
    }

    return render(request, 'almacen/producto_form.html', context)


@login_required
def movimientos(request):
    """Vista para listar movimientos del almacén"""
    movimientos_list = MovimientoAlmacen.objects.select_related(
        'producto', 'usuario'
    ).order_by('-fecha')

    context = {
        'movimientos': movimientos_list,
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': 'Movimientos', 'url': None}
        ]
    }

    return render(request, 'almacen/movimientos.html', context)


@login_required
def movimiento_nuevo(request):
    """Vista para crear un nuevo movimiento"""
    if request.method == 'POST':
        form = MovimientoAlmacenForm(request.POST)
        if form.is_valid():
            movimiento = form.save(commit=False)
            movimiento.usuario = request.user
            movimiento.save()
            messages.success(request, 'Movimiento registrado exitosamente.')
            return redirect('almacen:movimientos')
    else:
        form = MovimientoAlmacenForm()

    context = {
        'form': form,
        'titulo': 'Nuevo Movimiento',
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': 'Movimientos', 'url': 'almacen:movimientos'},
            {'name': 'Nuevo', 'url': None}
        ]
    }

    return render(request, 'almacen/movimiento_form.html', context)


@login_required
def categorias(request):
    """Vista para listar categorías"""
    categorias_list = Categoria.objects.filter(activo=True)

    context = {
        'categorias': categorias_list,
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': 'Categorías', 'url': None}
        ]
    }

    return render(request, 'almacen/categorias.html', context)


@login_required
def categoria_nueva(request):
    """Vista para crear una nueva categoría"""
    if request.method == 'POST':
        form = CategoriaForm(request.POST)
        if form.is_valid():
            categoria = form.save()
            messages.success(request, f'Categoría "{categoria.nombre}" creada exitosamente.')
            return redirect('almacen:categorias')
    else:
        form = CategoriaForm()

    context = {
        'form': form,
        'titulo': 'Nueva Categoría',
        'breadcrumb_items': [
            {'name': 'Almacén', 'url': 'almacen:productos'},
            {'name': 'Categorías', 'url': 'almacen:categorias'},
            {'name': 'Nueva', 'url': None}
        ]
    }

    return render(request, 'almacen/categoria_form.html', context)
