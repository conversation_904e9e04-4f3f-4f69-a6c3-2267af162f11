from django.contrib import admin
from .models import Categoria, Producto, MovimientoAlmacen


@admin.register(Categoria)
class CategoriaAdmin(admin.ModelAdmin):
    """Administración de categorías"""
    list_display = ['nombre', 'activo', 'fecha_creacion']
    list_filter = ['activo', 'fecha_creacion']
    search_fields = ['nombre']
    ordering = ['nombre']


@admin.register(Producto)
class ProductoAdmin(admin.ModelAdmin):
    """Administración de productos"""
    list_display = ['nombre', 'codigo', 'categoria', 'precio_compra', 'precio_venta', 'stock_actual', 'stock_minimo', 'activo']
    list_filter = ['categoria', 'activo', 'unidad_medida']
    search_fields = ['nombre', 'codigo']
    ordering = ['nombre']
    readonly_fields = ['fecha_creacion', 'fecha_modificacion']

    fieldsets = (
        ('Información básica', {
            'fields': ('nombre', 'codigo', 'categoria', 'unidad_medida')
        }),
        ('Precios', {
            'fields': ('precio_compra', 'precio_venta')
        }),
        ('Stock', {
            'fields': ('stock_actual', 'stock_minimo')
        }),
        ('Estado', {
            'fields': ('activo',)
        }),
        ('Fechas', {
            'fields': ('fecha_creacion', 'fecha_modificacion'),
            'classes': ('collapse',)
        }),
    )


@admin.register(MovimientoAlmacen)
class MovimientoAlmacenAdmin(admin.ModelAdmin):
    """Administración de movimientos de almacén"""
    list_display = ['fecha', 'producto', 'tipo', 'motivo', 'cantidad', 'usuario']
    list_filter = ['tipo', 'motivo', 'fecha']
    search_fields = ['producto__nombre', 'usuario__username']
    ordering = ['-fecha']
    readonly_fields = ['fecha']

    def save_model(self, request, obj, form, change):
        if not change:  # Solo para nuevos objetos
            obj.usuario = request.user
        super().save_model(request, obj, form, change)
