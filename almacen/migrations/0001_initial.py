# Generated by Django 5.2.1 on 2025-05-27 18:30

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Categoria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100, unique=True, verbose_name='Nombre')),
                ('descripcion', models.TextField(blank=True, verbose_name='Descripción')),
                ('activo', models.BooleanField(default=True, verbose_name='Activo')),
                ('fecha_creacion', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de creación')),
            ],
            options={
                'verbose_name': 'Categoría',
                'verbose_name_plural': 'Categorías',
                'ordering': ['nombre'],
            },
        ),
        migrations.CreateModel(
            name='Producto',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=200, verbose_name='Nombre del producto')),
                ('codigo', models.CharField(blank=True, max_length=50, unique=True, verbose_name='Código')),
                ('unidad_medida', models.CharField(choices=[('unidad', 'Unidad'), ('kg', 'Kilogramo'), ('litro', 'Litro'), ('caja', 'Caja'), ('paquete', 'Paquete')], default='unidad', max_length=20, verbose_name='Unidad de medida')),
                ('precio_compra', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Precio de compra')),
                ('precio_venta', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Precio de venta')),
                ('stock_actual', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Stock actual')),
                ('stock_minimo', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Stock mínimo')),
                ('activo', models.BooleanField(default=True, verbose_name='Activo')),
                ('fecha_creacion', models.DateTimeField(auto_now_add=True, verbose_name='Fecha de creación')),
                ('fecha_modificacion', models.DateTimeField(auto_now=True, verbose_name='Fecha de modificación')),
                ('categoria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='almacen.categoria', verbose_name='Categoría')),
            ],
            options={
                'verbose_name': 'Producto',
                'verbose_name_plural': 'Productos',
                'ordering': ['nombre'],
            },
        ),
        migrations.CreateModel(
            name='MovimientoAlmacen',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('entrada', 'Entrada'), ('salida', 'Salida')], max_length=10, verbose_name='Tipo de movimiento')),
                ('motivo', models.CharField(choices=[('compra', 'Compra'), ('devolucion', 'Devolución'), ('ajuste_inventario', 'Ajuste de inventario'), ('venta', 'Venta'), ('descarte', 'Descarte'), ('traslado', 'Traslado'), ('otro', 'Otro')], max_length=20, verbose_name='Motivo')),
                ('cantidad', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Cantidad')),
                ('precio_unitario', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Precio unitario')),
                ('observaciones', models.TextField(blank=True, verbose_name='Observaciones')),
                ('fecha', models.DateTimeField(auto_now_add=True, verbose_name='Fecha')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='Usuario')),
                ('producto', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='almacen.producto', verbose_name='Producto')),
            ],
            options={
                'verbose_name': 'Movimiento de almacén',
                'verbose_name_plural': 'Movimientos de almacén',
                'ordering': ['-fecha'],
            },
        ),
    ]
