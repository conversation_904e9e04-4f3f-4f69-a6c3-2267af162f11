/* Estilos personalizados para el sistema de gestión de tienda */

/* Animaciones suaves */
.transition-all {
    transition: all 0.3s ease;
}

/* Estilos para formularios */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm 
           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
           placeholder-gray-400 dark:placeholder-gray-500;
}

.form-select {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm 
           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm 
           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
           placeholder-gray-400 dark:placeholder-gray-500 resize-vertical;
}

/* Botones personalizados */
.btn-primary {
    @apply px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md 
           shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
           transition-colors duration-200;
}

.btn-secondary {
    @apply px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md 
           shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
           transition-colors duration-200;
}

.btn-success {
    @apply px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md 
           shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
           transition-colors duration-200;
}

.btn-danger {
    @apply px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md 
           shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2
           transition-colors duration-200;
}

.btn-warning {
    @apply px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-md 
           shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2
           transition-colors duration-200;
}

/* Tarjetas */
.card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
}

.card-body {
    @apply px-6 py-4;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750;
}

/* Tablas */
.table {
    @apply w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.table thead {
    @apply bg-gray-50 dark:bg-gray-700;
}

.table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}

.table tbody tr {
    @apply bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

/* Badges/Etiquetas */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.badge-warning {
    @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200;
}

.badge-danger {
    @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}

.badge-info {
    @apply bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200;
}

.badge-secondary {
    @apply bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
}

/* Indicadores de estado */
.status-indicator {
    @apply inline-block w-3 h-3 rounded-full mr-2;
}

.status-active {
    @apply bg-green-500;
}

.status-inactive {
    @apply bg-red-500;
}

.status-warning {
    @apply bg-yellow-500;
}

/* Estilos para estadísticas/métricas */
.metric-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700;
}

.metric-value {
    @apply text-3xl font-bold text-gray-900 dark:text-white;
}

.metric-label {
    @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}

.metric-change {
    @apply text-sm font-medium;
}

.metric-change.positive {
    @apply text-green-600 dark:text-green-400;
}

.metric-change.negative {
    @apply text-red-600 dark:text-red-400;
}

/* Estilos para loading/spinner */
.spinner {
    @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

/* Estilos para modales */
.modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center;
}

.modal-content {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4;
}

/* Responsive utilities */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
}

/* Animaciones personalizadas */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

/* Estilos para gráficos y charts */
.chart-container {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700;
}

/* Utilidades de color para las secciones */
.text-almacen {
    @apply text-blue-600 dark:text-blue-400;
}

.text-ventas {
    @apply text-green-600 dark:text-green-400;
}

.text-reportes {
    @apply text-orange-600 dark:text-orange-400;
}

.bg-almacen {
    @apply bg-blue-50 dark:bg-blue-900;
}

.bg-ventas {
    @apply bg-green-50 dark:bg-green-900;
}

.bg-reportes {
    @apply bg-orange-50 dark:bg-orange-900;
}
